'use client';

import { useState, useEffect, useRef } from 'react';
import {
  <PERSON>, Settings, RotateCcw, Save,
  Paintbrush, Sliders, X, Bell
} from 'lucide-react';
import { usePomodoroStore, TimerColorPreset, TimerUIStyle } from '@/lib/pomodoro-store';
import { TimerSettings as TimerSettingsType } from '@/lib/types';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogOverlay,
  DialogPortal
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

import { timerFormSchema, TimerFormValues } from '../timer';
import { TimerSettingsDialogProps } from './types';
import { TimerTab } from './timer-tab';
import { AppearanceTab } from './appearance-tab';
import { ControlsTab } from './controls-tab';
import { NotificationTab } from './notification-tab';

// Extracted common dialog content component with proper types
function DialogContentBody({
  activeTab,
  setActiveTab,
  form,
  handleNumberChange,
  pomodoroValue,
  shortBreakValue,
  longBreakValue,
  sessionsValue,
  timerModeValue,
  handleTimerModeChange,
  calculateTotalDuration,
  calculateEndTime,
  timerColor,
  setTimerColor,
  timerOpacity,
  handleOpacityChange,
  timerUIStyle,
  setTimerUIStyle,
  autoStartBreaks,
  setAutoStartBreaks,
  autoStartPomodoros,
  setAutoStartPomodoros,
  autoFullscreen,
  setAutoFullscreen,
  handleResetToDefaults,
  handleSaveSettings,
  onOpenChange
}: {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  form: UseFormReturn<TimerFormValues>;
  handleNumberChange: (field: keyof TimerFormValues, value: number) => void;
  pomodoroValue: number;
  shortBreakValue: number;
  longBreakValue: number;
  sessionsValue: number;
  timerModeValue: 'countDown' | 'countUp';
  handleTimerModeChange: (mode: 'countDown' | 'countUp') => void;
  calculateTotalDuration: (a: number, b: number, c: number, d: number) => string;
  calculateEndTime: (a: number, b: number, c: number, d: number) => string;
  timerColor: TimerColorPreset;
  setTimerColor: (color: TimerColorPreset) => void;
  timerOpacity: number;
  handleOpacityChange: (value: number) => void;
  timerUIStyle: TimerUIStyle;
  setTimerUIStyle: (style: TimerUIStyle) => void;
  autoStartBreaks: boolean;
  setAutoStartBreaks: (value: boolean) => void;
  autoStartPomodoros: boolean;
  setAutoStartPomodoros: (value: boolean) => void;
  autoFullscreen: boolean;
  setAutoFullscreen: (value: boolean) => void;
  handleResetToDefaults: () => void;
  handleSaveSettings: () => void;
  onOpenChange: (open: boolean) => void;
}) {
  // Tab color and icon mapping for consistent styling
  const tabConfig = {
    timer: {
      icon: <Clock className="h-4 w-4" />,
      color: "bg-blue-600",
      hoverColor: "hover:bg-blue-50 dark:hover:bg-blue-900/20",
      activeColor: "data-[state=active]:bg-blue-600 data-[state=active]:text-white",
      borderColor: "border-blue-200 dark:border-blue-800",
      iconColor: "text-blue-600 dark:text-blue-500"
    },
    appearance: {
      icon: <Paintbrush className="h-4 w-4" />,
      color: "bg-indigo-600",
      hoverColor: "hover:bg-indigo-50 dark:hover:bg-indigo-900/20",
      activeColor: "data-[state=active]:bg-indigo-600 data-[state=active]:text-white",
      borderColor: "border-indigo-200 dark:border-indigo-800",
      iconColor: "text-indigo-600 dark:text-indigo-500"
    },
    controls: {
      icon: <Sliders className="h-4 w-4" />,
      color: "bg-purple-600",
      hoverColor: "hover:bg-purple-50 dark:hover:bg-purple-900/20",
      activeColor: "data-[state=active]:bg-purple-600 data-[state=active]:text-white",
      borderColor: "border-purple-200 dark:border-purple-800",
      iconColor: "text-purple-600 dark:text-purple-500"
    },
    notification: {
      icon: <Bell className="h-4 w-4" />,
      color: "bg-emerald-600",
      hoverColor: "hover:bg-emerald-50 dark:hover:bg-emerald-900/20",
      activeColor: "data-[state=active]:bg-emerald-600 data-[state=active]:text-white",
      borderColor: "border-emerald-200 dark:border-emerald-800",
      iconColor: "text-emerald-600 dark:text-emerald-500"
    }
  };

  return (
    <>
      {/* Dialog Header */}
      <div className="sticky top-0 z-10 bg-background/80 dark:bg-gray-950/80 backdrop-blur-sm flex-shrink-0 border-b border-border/30">
        <DialogHeader className="px-4 pt-3 pb-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2 text-base font-medium text-foreground/90">
              <Settings className="h-4 w-4 text-primary/70" />
              <span>Timer Settings</span>
            </DialogTitle>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleResetToDefaults}
                className="h-6 w-6 rounded-md hover:bg-muted/50 transition-colors cursor-pointer"
                title="Reset to defaults"
              >
                <RotateCcw className="h-3 w-3 text-muted-foreground" />
                <span className="sr-only">Reset to defaults</span>
              </Button>
              <DialogPrimitive.Close asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 rounded-md hover:bg-muted/50 transition-colors cursor-pointer"
                  title="Close"
                >
                  <X className="h-3 w-3 text-muted-foreground" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogPrimitive.Close>
            </div>
          </div>
        </DialogHeader>

        {/* Tabs navigation */}
        <div className="px-4">
          <Tabs
            defaultValue="timer"
            className="w-full"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <TabsList className="w-full grid grid-cols-4 gap-1 bg-muted/50 p-0.5 rounded-md h-auto">
              {Object.entries(tabConfig).map(([key, config]) => (
                <TabsTrigger
                  key={key}
                  value={key}
                  className={`flex items-center justify-center gap-1 py-1.5 px-2 rounded-sm transition-all text-xs font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground hover:bg-background/50`}
                >
                  <span className={activeTab === key ? "text-primary/80" : "text-muted-foreground"}>
                    {config.icon}
                  </span>
                  <span className="hidden sm:inline">{key.charAt(0).toUpperCase() + key.slice(1)}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            <div className="flex-grow overflow-hidden">
              <ScrollArea
                className="h-[calc(65vh-12rem)] min-h-[180px] max-h-[calc(65vh-10rem)] sm:h-[calc(70vh-10rem)] sm:max-h-[calc(70vh-8rem)] timer-settings-scroll"
                style={{
                  WebkitOverflowScrolling: 'touch',
                  touchAction: 'pan-y',
                  overscrollBehavior: 'contain'
                }}
              >
                <div className="pt-0 pb-4 pr-3">
                  <Form {...form}>
                    <form className="space-y-3 relative min-h-[50px] pb-2 mb-0">
                      {/* Timer Settings Tab */}
                      <TabsContent value="timer" className="focus-visible:outline-none focus-visible:ring-0 mt-0 pb-0 transition-opacity duration-200 ease-in-out">
                        <div className="bg-card/50 border border-border/40 rounded-md p-2.5 shadow-sm mr-1">
                          <TimerTab
                            form={form}
                            handleNumberChange={handleNumberChange}
                            pomodoroValue={pomodoroValue}
                            shortBreakValue={shortBreakValue}
                            longBreakValue={longBreakValue}
                            sessionsValue={sessionsValue}
                            timerModeValue={timerModeValue}
                            handleTimerModeChange={handleTimerModeChange}
                            calculateTotalDuration={calculateTotalDuration}
                            calculateEndTime={calculateEndTime}
                          />
                        </div>
                      </TabsContent>

                      {/* Appearance Tab */}
                      <TabsContent value="appearance" className="focus-visible:outline-none focus-visible:ring-0 mt-0 pb-0 transition-opacity duration-200 ease-in-out">
                        <div className="bg-card/50 border border-border/40 rounded-md p-2.5 shadow-sm mr-1">
                          <AppearanceTab
                            timerColor={timerColor}
                            setTimerColor={setTimerColor}
                            timerOpacity={timerOpacity}
                            handleOpacityChange={handleOpacityChange}
                            timerUIStyle={timerUIStyle}
                            setTimerUIStyle={setTimerUIStyle}
                          />
                        </div>
                      </TabsContent>

                      {/* Controls Tab */}
                      <TabsContent value="controls" className="focus-visible:outline-none focus-visible:ring-0 mt-0 pb-0 transition-opacity duration-200 ease-in-out">
                        <div className="bg-card/50 border border-border/40 rounded-md p-2.5 shadow-sm mr-1">
                          <ControlsTab
                            autoStartBreaks={autoStartBreaks}
                            setAutoStartBreaks={setAutoStartBreaks}
                            autoStartPomodoros={autoStartPomodoros}
                            setAutoStartPomodoros={setAutoStartPomodoros}
                            autoFullscreen={autoFullscreen}
                            setAutoFullscreen={setAutoFullscreen}
                          />
                        </div>
                      </TabsContent>

                      {/* Notification Tab */}
                      <TabsContent value="notification" className="focus-visible:outline-none focus-visible:ring-0 mt-0 pb-0 transition-opacity duration-200 ease-in-out">
                        <div className="bg-card/50 border border-border/40 rounded-md p-2.5 shadow-sm mr-1">
                          <NotificationTab />
                        </div>
                      </TabsContent>
                    </form>
                  </Form>
                </div>
              </ScrollArea>
            </div>
          </Tabs>
        </div>
      </div>

      {/* Dialog Footer */}
      <DialogFooter className="flex flex-row items-center justify-end gap-2 px-4 py-2.5 flex-shrink-0 border-t border-border/30">
        <DialogPrimitive.Close asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs cursor-pointer"
          >
            Cancel
          </Button>
        </DialogPrimitive.Close>
        <Button
          onClick={handleSaveSettings}
          size="sm"
          className="h-7 text-xs bg-primary hover:bg-primary/90 gap-1 cursor-pointer"
        >
          <Save className="h-3 w-3" />
          Save
        </Button>
      </DialogFooter>
    </>
  );
}

export function TimerSettings({ isOpen, onOpenChange, noDimmer = false }: TimerSettingsDialogProps) {
  const {
    timerSettings,
    updateSettings,
    timerColor,
    setTimerColor,
    timerOpacity,
    setTimerOpacity,
    timerUIStyle,
    setTimerUIStyle,
    autoStartBreaks,
    setAutoStartBreaks,
    autoStartPomodoros,
    setAutoStartPomodoros,
    autoFullscreen,
    setAutoFullscreen
  } = usePomodoroStore();
  const [activeTab, setActiveTab] = useState('timer');
  const previousIsOpen = useRef(isOpen);

  // Calculate total duration in minutes
  const calculateTotalDuration = (
    pomodoroMinutes: number,
    shortBreakMinutes: number,
    longBreakMinutes: number,
    sessionsCount: number
  ): string => {
    // Total pomodoro time
    const totalPomodoroTime = pomodoroMinutes * sessionsCount;

    // Total short break time (there are sessionsCount-1 short breaks)
    const totalShortBreakTime = shortBreakMinutes * (sessionsCount - 1);

    // Long break happens once
    const totalLongBreakTime = longBreakMinutes;

    // Total time in minutes
    const totalMinutes = totalPomodoroTime + totalShortBreakTime + totalLongBreakTime;

    // Convert to hours and minutes
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours > 0) {
      return `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`;
    }
    return `${minutes}m`;
  };

  // Calculate end time based on current time
  const calculateEndTime = (
    pomodoroMinutes: number,
    shortBreakMinutes: number,
    longBreakMinutes: number,
    sessionsCount: number
  ): string => {
    const now = new Date();

    // Total pomodoro time
    const totalPomodoroTime = pomodoroMinutes * sessionsCount;

    // Total short break time (there are sessionsCount-1 short breaks)
    const totalShortBreakTime = shortBreakMinutes * (sessionsCount - 1);

    // Long break happens once
    const totalLongBreakTime = longBreakMinutes;

    // Total time in minutes
    const totalMinutes = totalPomodoroTime + totalShortBreakTime + totalLongBreakTime;

    // Add minutes to current time
    const endTime = new Date(now.getTime() + totalMinutes * 60000);

    // Format as hh:mm
    return endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false });
  };

  // Create form with current settings
  const form = useForm<TimerFormValues>({
    resolver: zodResolver(timerFormSchema),
    defaultValues: {
      pomodoroMinutes: timerSettings.pomodoroMinutes,
      shortBreakMinutes: timerSettings.shortBreakMinutes,
      longBreakMinutes: timerSettings.longBreakMinutes,
      sessionsBeforeLongBreak: timerSettings.sessionsBeforeLongBreak,
      timerMode: timerSettings.timerMode || 'countDown',
    },
  });

  // Reset form only when dialog opens (not during edits)
  useEffect(() => {
    // Only reset when dialog transitions from closed to open
    if (isOpen && !previousIsOpen.current) {
      form.reset({
        pomodoroMinutes: timerSettings.pomodoroMinutes,
        shortBreakMinutes: timerSettings.shortBreakMinutes,
        longBreakMinutes: timerSettings.longBreakMinutes,
        sessionsBeforeLongBreak: timerSettings.sessionsBeforeLongBreak,
        timerMode: timerSettings.timerMode || 'countDown',
      });
    }

    // Update previous state
    previousIsOpen.current = isOpen;
  }, [isOpen, form, timerSettings]);

  // Handler for updating number value
  const handleNumberChange = (field: keyof TimerFormValues, value: number) => {
    // Update the form field
    form.setValue(field, value, { shouldValidate: true });
  };

  // Handler for updating timer mode
  const handleTimerModeChange = (mode: 'countDown' | 'countUp') => {
    form.setValue('timerMode', mode, { shouldValidate: true });
  };

  // Reset to defaults handler
  const handleResetToDefaults = () => {
    const defaults = {
      pomodoroMinutes: 25,
      shortBreakMinutes: 5,
      longBreakMinutes: 15,
      sessionsBeforeLongBreak: 4,
      timerMode: 'countDown' as const,
    };

    form.reset(defaults);
  };

  // Save settings handler
  const handleSaveSettings = () => {
    const currentValues = form.getValues();

    // Update timer settings
    updateSettings({
      pomodoroMinutes: currentValues.pomodoroMinutes,
      shortBreakMinutes: currentValues.shortBreakMinutes,
      longBreakMinutes: currentValues.longBreakMinutes,
      sessionsBeforeLongBreak: currentValues.sessionsBeforeLongBreak,
      timerMode: currentValues.timerMode,
    });

    // Close dialog
    onOpenChange(false);
  };

  // Handler for updating opacity
  const handleOpacityChange = (value: number) => {
    setTimerOpacity(value);
  };

  // Get values for the UI
  const pomodoroValue = form.watch('pomodoroMinutes');
  const shortBreakValue = form.watch('shortBreakMinutes');
  const longBreakValue = form.watch('longBreakMinutes');
  const sessionsValue = form.watch('sessionsBeforeLongBreak');
  const timerModeValue = form.watch('timerMode');

  // Common props for DialogContentBody
  const dialogContentProps = {
    activeTab,
    setActiveTab,
    form,
    handleNumberChange,
    pomodoroValue,
    shortBreakValue,
    longBreakValue,
    sessionsValue,
    timerModeValue,
    handleTimerModeChange,
    calculateTotalDuration,
    calculateEndTime,
    timerColor,
    setTimerColor,
    timerOpacity,
    handleOpacityChange,
    timerUIStyle,
    setTimerUIStyle,
    autoStartBreaks,
    setAutoStartBreaks,
    autoStartPomodoros,
    setAutoStartPomodoros,
    autoFullscreen,
    setAutoFullscreen,
    handleResetToDefaults,
    handleSaveSettings,
    onOpenChange
  };

  // Common content wrapper styles with hover opacity for low opacity settings
  const contentWrapperClasses = cn(
    "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] duration-200 sm:max-w-lg md:max-w-xl p-0 overflow-hidden backdrop-blur-sm rounded-lg border border-border/50 shadow-xl flex flex-col transition-opacity duration-200 max-h-[65vh] sm:max-h-[70vh]",
    // Add hover opacity when timer opacity is very low
    timerOpacity <= 10 ? "hover:bg-background/90 hover:backdrop-blur-md" : ""
  );

  // Mobile-optimized styles
  const mobileOptimizedStyles: React.CSSProperties = {
    WebkitOverflowScrolling: 'touch' as const,
    touchAction: 'manipulation' as const,
    overscrollBehavior: 'contain' as const
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {noDimmer ? (
        <DialogPortal>
          <DialogOverlay className="bg-transparent" />
          <DialogPrimitive.Content 
            className={contentWrapperClasses}
            style={mobileOptimizedStyles}
            onMouseDown={(e) => e.stopPropagation()}
            onDoubleClick={(e) => e.stopPropagation()}
          >
            <DialogContentBody {...dialogContentProps} />
          </DialogPrimitive.Content>
        </DialogPortal>
      ) : (
        <DialogContent
          className={cn(
            "sm:max-w-lg md:max-w-xl p-0 gap-0 overflow-hidden bg-background/95 dark:bg-gray-950/95 backdrop-blur-sm rounded-lg border border-border/50 shadow-xl flex flex-col transition-opacity duration-200 max-h-[65vh] sm:max-h-[70vh]",
            // Add hover opacity when timer opacity is very low
            timerOpacity <= 10 ? "hover:bg-background/98 dark:hover:bg-gray-950/98 hover:backdrop-blur-md" : ""
          )}
          style={mobileOptimizedStyles}
          onMouseDown={(e) => e.stopPropagation()}
          onDoubleClick={(e) => e.stopPropagation()}
        >
          <DialogContentBody {...dialogContentProps} />
        </DialogContent>
      )}
    </Dialog>
  );
}
